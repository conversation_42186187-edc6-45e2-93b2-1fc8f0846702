#pragma once

#include <string>
#include <memory>

// Configuration structure to hold all settings
struct AppConfig {
    // Keyboard settings
    struct KeyboardSettings {
        bool auto_layout = true;
        float keyboard_margin = 50.0f;
        float white_key_width = 20.0f;
        float white_key_height = 120.0f;
        float black_key_width = 12.0f;
        float black_key_height = 80.0f;
    } keyboard;

    // Audio settings
    struct AudioSettings {
        bool audio_enabled = true;
        float volume = 0.8f;
        std::string soundfont_path = "default.sf2";
        int polyphony = 256;
        bool limiter_enabled = false;

        // Audio Limiter settings
        float limiter_threshold = -6.0f;      // dB
        float limiter_release_time = 100.0f;  // ms
        float limiter_lookahead_time = 5.0f;  // ms

        // BASS FX settings
        bool bassfx_enabled = false;           // Enable/disable BASS FX effects
        bool bassfx_reverb_enabled = false;    // Reverb effect
        bool bassfx_chorus_enabled = false;    // Chorus effect
        bool bassfx_echo_enabled = false;      // Echo effect
        bool bassfx_compressor_enabled = false; // Compressor effect

        // BASS FX effect parameters
        float bassfx_reverb_mix = 0.3f;        // Reverb wet/dry mix (0.0-1.0)
        float bassfx_chorus_mix = 0.2f;        // Chorus wet/dry mix (0.0-1.0)
        float bassfx_echo_mix = 0.15f;         // Echo wet/dry mix (0.0-1.0)
        float bassfx_compressor_ratio = 4.0f;  // Compressor ratio (1.0-20.0)
    } audio;

    // Display settings
    struct DisplaySettings {
        float background_color[3] = {0.45f, 0.55f, 0.60f}; // RGB (legacy, kept for compatibility)
        int background_mode = 1; // 0=solid, 1=radial gradient, 2=transparent, 3=image
        float gradient_center_color[3] = {0.2f, 0.3f, 0.5f}; // RGB for center color
        float gradient_edge_color[3] = {0.05f, 0.05f, 0.1f}; // RGB for edge color
        float transparency_alpha = 0.8f; // Transparency level (0.0=fully transparent, 1.0=opaque)
        std::string background_image_path = ""; // Path to background image
        float background_image_opacity = 1.0f; // Image opacity (0.0-1.0)
        int background_image_scale_mode = 0; // 0=stretch, 1=fit, 2=fill, 3=tile
        bool show_settings = true;
        bool show_debug = true;
        bool show_bassmidi_status = false;
        bool show_midi_input = false;
        bool show_audio_limiter = false;
    } display;

    // MIDI settings
    struct MIDISettings {
        int selected_midi_device = -1;
        int selected_alsa_midi_device = -1;
        bool use_alsa_midi = false;

        // External Process MIDI settings
        bool use_ext_process_midi = false;
        std::string ext_process_executable_path = "";
        std::string ext_process_arguments = "";
        bool ext_process_enabled = false;
    } midi;

    // Window settings
    struct WindowSettings {
        int width = 1200;
        int height = 800;
        bool maximized = false;
        bool transparent_framebuffer = true; // Enable window transparency for aero effects
    } window;

    // Cheat Tool settings
    struct CheatToolSettings {
        bool show_cheat_tool = false;
        bool multioctave_enabled = false;
        int multioctave_count = 1;      // How many additional octaves to play (1 = one above and one below)
        int multioctave_distance = 12;  // Distance in semitones (12 = one octave)
    } cheat_tool;
};

class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager();

    // Initialize the config manager with config file path
    bool Initialize(const std::string& config_file_path = "");

    // Load configuration from file
    bool LoadConfig();

    // Save configuration to file
    bool SaveConfig();

    // Auto-save configuration (called when settings change)
    void AutoSave();

    // Get configuration reference
    AppConfig& GetConfig() { return config_; }
    const AppConfig& GetConfig() const { return config_; }

    // Get config file path
    const std::string& GetConfigFilePath() const { return config_file_path_; }

    // Check if auto-save is enabled
    bool IsAutoSaveEnabled() const { return auto_save_enabled_; }

    // Enable/disable auto-save
    void SetAutoSaveEnabled(bool enabled) { auto_save_enabled_ = enabled; }

    // Mark config as dirty (needs saving)
    void MarkDirty() { config_dirty_ = true; }

    // Check if config is dirty
    bool IsDirty() const { return config_dirty_; }

    // Reset to default configuration
    void ResetToDefaults();

    // Get default config file path based on platform
    static std::string GetDefaultConfigPath();

private:
    AppConfig config_;
    std::string config_file_path_;
    bool auto_save_enabled_;
    bool config_dirty_;

    // Helper functions for JSON serialization
    bool LoadFromJSON(const std::string& json_content);
    std::string SaveToJSON() const;

    // Platform-specific config directory functions
    static std::string GetConfigDirectory();
    static std::string GetExecutableDirectory();
    
    // File I/O helpers
    bool ReadFile(const std::string& file_path, std::string& content);
    bool WriteFile(const std::string& file_path, const std::string& content);
    bool CreateDirectoryIfNotExists(const std::string& dir_path);
};
